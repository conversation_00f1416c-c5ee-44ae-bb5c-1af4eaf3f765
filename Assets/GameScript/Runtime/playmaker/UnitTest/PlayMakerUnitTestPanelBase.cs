using System;
using UnityEngine;

namespace Fish.PlayMaker
{
    public abstract class PlayMakerUnitTestPanelBase : MonoBehaviour
    {
        protected abstract string FsmName { get; }

        private void OnEnable()
        {
            
        }
        
        private void StateFail()
        {
        }

        protected void Call(string eventName)
        {
            var fsm = FindFSMByName(FsmName);
            if (fsm != null)
            {
                fsm.Fsm.Event(eventName);
            }
            else
            {
                Debug.LogError($"未找到名为 {FsmName} 的 FSM 组件");
            }
        }

        private PlayMakerFSM FindFSMByName(string fsmName)
        {
            PlayMakerFSM[] fsmComponents = GetComponents<PlayMakerFSM>();
            foreach (var fsm in fsmComponents)
            {
                if (fsm.FsmName == fsmName)
                {
                    return fsm;
                }
            }

            return null;
        }
    }
}