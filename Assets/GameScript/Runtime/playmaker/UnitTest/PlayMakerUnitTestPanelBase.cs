using UnityEngine;

namespace Fish.PlayMaker
{
    public class PlayMakerUnitTestPanelBase : MonoBehaviour
    {
        protected void Call(string fsmName, string eventName)
        {
            var fsm = FindFSMByName(fsmName);
            if (fsm != null)
            {
                fsm.Fsm.Event(eventName);
            }
            else
            {
                Debug.LogError($"未找到名为 {fsmName} 的 FSM 组件");
            }
        }

        private PlayMakerFSM FindFSMByName(string fsmName)
        {
            PlayMakerFSM[] fsmComponents = GetComponents<PlayMakerFSM>();
            foreach (var fsm in fsmComponents)
            {
                if (fsm.FsmName == fsmName)
                {
                    return fsm;
                }
            }

            return null;
        }
    }
}