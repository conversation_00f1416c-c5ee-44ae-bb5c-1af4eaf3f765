using UnityEngine;
using UnityEngine.UI;

namespace Fish.PlayMaker
{
    public class PlayMakerUnitTest_Audio : PlayMakerUnitTestPanelBase
    {
        protected override string FsmName => "Audio";
        
        public Button btnPlay;
        public Button btnStop;
        
        private void Awake()
        {
            btnPlay.onClick.AddListener(OnBtnPlay);
            btnStop.onClick.AddListener(OnBtnStop);
        }

        private void OnBtnStop()
        {
            Call("ClickStop");
        }

        private void OnBtnPlay()
        {
            Call("ClickPlay");
        }

    }
}