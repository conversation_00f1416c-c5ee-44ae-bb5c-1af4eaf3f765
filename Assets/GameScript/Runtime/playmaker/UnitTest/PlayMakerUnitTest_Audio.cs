using UnityEngine;
using UnityEngine.UI;

namespace Fish.PlayMaker
{
    public class PlayMakerUnitTest_Audio : PlayMakerUnitTestPanelBase
    {
        public Button btnPlay;
        public Button btnStop;
        
        private void Awake()
        {
            btnPlay.onClick.AddListener(OnBtnPlay);
            btnStop.onClick.AddListener(OnBtnStop);
        }

        private void OnBtnStop()
        {
            
        }

        private void OnBtnPlay()
        {
            
        }
    }
}