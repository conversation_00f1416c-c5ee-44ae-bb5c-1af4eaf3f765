using System;
using UnityEngine;
using UnityEngine.UI;

namespace Fish.PlayMaker
{
    public class PlayMakerUnitTest : MonoBehaviour
    {
        #region 按钮

        public Button btnAudio;
        public Button btnAvatar;
        public Button btnCamera;
        public Button btnCommon;
        public Button btnDataStore;
        public Button btnEvent;
        public Button btnGameEffect;
        public Button btnGameObject;
        public Button btnJson;
        public Button btnNetwork;
        public Button btnReflect;
        public Button btnTextMesh;
        public Button btnUI;

        #endregion

        public GameObject goEntrance;
        public GameObject goPanel;
        public Image ImgStatus;

        #region 各功能面板
        public GameObject goPanelAudio;
        public GameObject goPanelAvatar;
        public GameObject goPanelCamera;
        public GameObject goPanelCommon;
        public GameObject goPanelDataStore;
        public GameObject goPanelEvent;
        public GameObject goPanelGameEffect;
        public GameObject goPanelGameObject;
        public GameObject goPanelJson;
        public GameObject goPanelNetwork;
        public GameObject goPanelReflect;
        public GameObject goPanelTextMesh;
        public GameObject goPanelUI;
        #endregion

        private void Awake()
        {
            BindUIEvent();
        }

        private void BindUIEvent()
        {
            // 绑定所有按钮的点击事件
            btnAudio?.onClick.AddListener(OnBtnAudio);
            btnAvatar?.onClick.AddListener(OnBtnAvatar);
            btnCamera?.onClick.AddListener(OnBtnCamera);
            btnCommon?.onClick.AddListener(OnBtnCommon);
            btnDataStore?.onClick.AddListener(OnBtnDataStore);
            btnEvent?.onClick.AddListener(OnBtnEvent);
            btnGameEffect?.onClick.AddListener(OnBtnGameEffect);
            btnGameObject?.onClick.AddListener(OnBtnGameObject);
            btnJson?.onClick.AddListener(OnBtnJson);
            btnNetwork?.onClick.AddListener(OnBtnNetwork);
            btnReflect?.onClick.AddListener(OnBtnReflect);
            btnTextMesh?.onClick.AddListener(OnBtnTextMesh);
            btnUI?.onClick.AddListener(OnBtnUI);
        }

        public void StateSuccess()
        {
            ImgStatus.color = Color.green;
        }
        
        public void StateFail()
        {
            ImgStatus.color = Color.red;
        }

        public void StateDoing()
        {
            ImgStatus.color = Color.yellow;
        }

        public void OnBtnAudio()
        {
        }

        public void OnBtnAvatar()
        {
        }

        public void OnBtnCamera()
        {
        }

        public void OnBtnCommon()
        {
        }

        public void OnBtnDataStore()
        {
        }

        public void OnBtnEvent()
        {
        }

        public void OnBtnGameEffect()
        {
        }

        public void OnBtnGameObject()
        {
        }

        public void OnBtnJson()
        {
        }

        public void OnBtnNetwork()
        {
        }

        public void OnBtnReflect()
        {
        }

        public void OnBtnTextMesh()
        {
        }

        public void OnBtnUI()
        {
        }
    }
}