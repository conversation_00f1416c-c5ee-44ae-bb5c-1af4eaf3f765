using System;
using UnityEngine;
using UnityEngine.UIElements;
using Image = UnityEngine.UI.Image;

namespace Fish.PlayMaker
{
    public class PlayMakerUnitTest : MonoBehaviour
    {
        #region 按钮

        public Button btnAudio;
        public Button btnAvatar;
        public Button btnCamera;
        public Button btnCommon;
        public Button btnDataStore;
        public Button btnEvent;
        public Button btnGameEffect;
        public Button btnGameObject;
        public Button btnJson;
        public Button btnNetwork;
        public Button btnReflect;
        public Button btnTextMesh;
        public Button btnUI;

        #endregion

        public GameObject goEntrance;
        public GameObject goPanel;
        public Image ImgStatus;

        #region 各功能面板
        public GameObject goPanelAudio;
        public GameObject goPanelAvatar;
        public GameObject goPanelCamera;
        public GameObject goPanelCommon;
        public GameObject goPanelDataStore;
        public GameObject goPanelEvent;
        public GameObject goPanelGameEffect;
        public GameObject goPanelGameObject;
        public GameObject goPanelJson;
        public GameObject goPanelNetwork;
        public GameObject goPanelReflect;
        public GameObject goPanelTextMesh;
        public GameObject goPanelUI;
        #endregion

        private void Awake()
        {
            BindUIEvent();
        }

        private void BindUIEvent()
        {
            // 绑定所有按钮的点击事件
            if (btnAudio != null)
                btnAudio.clicked += OnBtnAudio;

            if (btnAvatar != null)
                btnAvatar.clicked += OnBtnAvatar;

            if (btnCamera != null)
                btnCamera.clicked += OnBtnCamera;

            if (btnCommon != null)
                btnCommon.clicked += OnBtnCommon;

            if (btnDataStore != null)
                btnDataStore.clicked += OnBtnDataStore;

            if (btnEvent != null)
                btnEvent.clicked += OnBtnEvent;

            if (btnGameEffect != null)
                btnGameEffect.clicked += OnBtnGameEffect;

            if (btnGameObject != null)
                btnGameObject.clicked += OnBtnGameObject;

            if (btnJson != null)
                btnJson.clicked += OnBtnJson;

            if (btnNetwork != null)
                btnNetwork.clicked += OnBtnNetwork;

            if (btnReflect != null)
                btnReflect.clicked += OnBtnReflect;

            if (btnTextMesh != null)
                btnTextMesh.clicked += OnBtnTextMesh;

            if (btnUI != null)
                btnUI.clicked += OnBtnUI;
        }

        public void StateSuccess()
        {
            ImgStatus.color = Color.green;
        }
        
        public void StateFail()
        {
            ImgStatus.color = Color.red;
        }

        public void StateDoing()
        {
            ImgStatus.color = Color.yellow;
        }

        public void OnBtnAudio()
        {
        }

        public void OnBtnAvatar()
        {
        }

        public void OnBtnCamera()
        {
        }

        public void OnBtnCommon()
        {
        }

        public void OnBtnDataStore()
        {
        }

        public void OnBtnEvent()
        {
        }

        public void OnBtnGameEffect()
        {
        }

        public void OnBtnGameObject()
        {
        }

        public void OnBtnJson()
        {
        }

        public void OnBtnNetwork()
        {
        }

        public void OnBtnReflect()
        {
        }

        public void OnBtnTextMesh()
        {
        }

        public void OnBtnUI()
        {
        }
    }
}