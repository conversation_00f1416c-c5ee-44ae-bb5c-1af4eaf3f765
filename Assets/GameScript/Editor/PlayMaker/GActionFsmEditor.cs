using UnityEditor;
using HutongGames.PlayMakerEditor;
using Fish.PlayMaker;
using UnityEngine;
using System.Collections.Generic;
using System;

namespace Fish.PlayMakerEditor
{
	[CustomActionEditor(typeof(GActionFsm))]
	public class GActionUniversalFsmEditor : CustomActionEditor
	{
		public override bool OnGUI()
		{
			var action = target as GActionFsm;
			EditField("UseStoreTarget");
			if (action.UseStoreTarget)
			{
				EditField("StoreTargetKey");
			}
			else
			{
				EditField("targetObject");
			}

			EditorGUILayout.Space();

			DrawSubActionProperties(action.Action, 0);

			EditField("finishedEvent");

			return GUI.changed;
		}

		private void DrawSubActionProperties(PMGAction action, int depth, List<GActionFsm.ActionType> typePopupFilterList = null)
		{
			// 显示动作类型选择
			GActionFsm.ActionType newActionType;
			if (typePopupFilterList == null)
			{
				newActionType = (GActionFsm.ActionType)EditorGUILayout.EnumPopup("Type", action.ActionType);
			}
			else
			{
				// 创建过滤后的枚举选项
				var filteredOptions = new List<string>();
				var filteredValues = new List<GActionFsm.ActionType>();

				foreach (GActionFsm.ActionType actionType in System.Enum.GetValues(typeof(GActionFsm.ActionType)))
				{
					if (typePopupFilterList.Contains(actionType))
					{
						filteredOptions.Add(actionType.ToString());
						filteredValues.Add(actionType);
					}
				}

				int currentIndex = filteredValues.IndexOf(action.ActionType);
				if (currentIndex == -1) currentIndex = 0; // 如果当前值不在过滤列表中，默认选择第一个

				int newIndex = EditorGUILayout.Popup("Type", currentIndex, filteredOptions.ToArray());
				newActionType = filteredValues[newIndex];
			}

			if (newActionType != action.ActionType)
			{
				action.ActionType = newActionType;
				action.OnActionTypeChanged(newActionType);
			}

			// 根据类型显示对应的属性
			switch (action.ActionType)
			{
				case GActionFsm.ActionType.Show:
					break;
				case GActionFsm.ActionType.Hide:
					break;
				case GActionFsm.ActionType.RemoveSelf:
					break;
				case GActionFsm.ActionType.FlipX:
					break;
				case GActionFsm.ActionType.FlipY:
					break;

				case GActionFsm.ActionType.CallFunc:
					{
						EditorGUILayout.LabelField("Settings", EditorStyles.boldLabel);
						var param = action.ParamCallFunc;

						DrawParam("FsmName", param.fsmName);
						DrawParam("EventName", param.eventName);

						EditorGUILayout.Space();
					}
					break;
				case GActionFsm.ActionType.DelayTime:
					{
						EditorGUILayout.LabelField("Settings", EditorStyles.boldLabel);
						var param = action.ParamDelayTime;

						DrawParam("Duration", param.duration);
					}
					break;
				case GActionFsm.ActionType.MoveBy:
					{
						EditorGUILayout.LabelField("Settings", EditorStyles.boldLabel);
						var param = action.ParamMoveBy;

						DrawParam("Duration", param.duration);
						DrawParam("DeltaPosition", param.deltaPosition);
						DrawParam("WorldPosition", param.worldPosition);
					}
					break;

				case GActionFsm.ActionType.MoveTo:
					{
						EditorGUILayout.LabelField("Settings", EditorStyles.boldLabel);

						var param = action.ParamMoveTo;

						DrawParam("Duration", param.duration);
						DrawParam("Target", param.target);
						DrawParam("WorldPosition", param.worldPosition);
					}

					break;
				case GActionFsm.ActionType.RotateBy:
					{
						EditorGUILayout.LabelField("Settings", EditorStyles.boldLabel);
						var param = action.ParamRotateBy;

						DrawParam("Duration", param.duration);
						DrawParam("DeltaAngle", param.deltaAngle);
					}
					break;
				case GActionFsm.ActionType.RotateTo:
					{
						EditorGUILayout.LabelField("Settings", EditorStyles.boldLabel);
						var param = action.ParamRotateTo;

						DrawParam("Duration", param.duration);
						DrawParam("DestAngle", param.destAngle);
					}
					break;
				case GActionFsm.ActionType.RotateBy3D:
					{
						EditorGUILayout.LabelField("Settings", EditorStyles.boldLabel);
						var param = action.ParamRotateBy3D;

						DrawParam("Duration", param.duration);
						DrawParam("DeltaAngle", param.DeltaAngle);
					}
					break;
				case GActionFsm.ActionType.ScaleTo:
					{
						EditorGUILayout.LabelField("Settings", EditorStyles.boldLabel);
						var param = action.ParamScaleTo;

						DrawParam("Duration", param.duration);
						DrawParam("Scale", param.Scale);
					}
					break;
				case GActionFsm.ActionType.ScaleBy:
					{
						EditorGUILayout.LabelField("Settings", EditorStyles.boldLabel);
						var param = action.ParamScaleBy;

						DrawParam("Duration", param.duration);
						DrawParam("Scale", param.Scale);
					}
					break;
				case GActionFsm.ActionType.FadeTo:
					{
						EditorGUILayout.LabelField("Settings", EditorStyles.boldLabel);
						var param = action.ParamFadeTo;

						DrawParam("Duration", param.duration);
						DrawParam("Alpha", param.alpha);
					}
					break;
				case GActionFsm.ActionType.TintTo:
					{
						EditorGUILayout.LabelField("Settings", EditorStyles.boldLabel);
						var param = action.ParamTintTo;

						DrawParam("Duration", param.duration);
						DrawParam("Color", param.Color);
					}
					break;
				case GActionFsm.ActionType.BlendTo:
					{
						EditorGUILayout.LabelField("Settings", EditorStyles.boldLabel);
						var param = action.ParamBlendTo;

						DrawParam("Duration", param.duration);
						DrawParam("Alpha", param.alpha);
					}
					break;
				case GActionFsm.ActionType.Flash:
					break;
				case GActionFsm.ActionType.FlashWithCurve:
					{
						EditorGUILayout.LabelField("Settings", EditorStyles.boldLabel);
						var param = action.ParamFlashWithCurve;

						DrawParam("UseDefaultCurve", param.useDefaultCurve);
						if (param.useDefaultCurve.GetValue(target.Owner.gameObject))
						{
						}
						else
						{
							DrawParam("Duration", param.duration);
							DrawParam("Curve", param.curve);
						}
					}
					break;
				case GActionFsm.ActionType.BezierTo:
					{
						EditorGUILayout.LabelField("Settings", EditorStyles.boldLabel);
						var param = action.ParamBezierTo;

						DrawParam("Duration", param.duration);
						DrawParam("P0", param.p0);
						DrawParam("P1", param.p1);
						DrawParam("P2", param.p2);
						DrawParam("P3", param.p3);
					}
					break;
				case GActionFsm.ActionType.CanvasGroupAlphaFadeTo:
					{
						EditorGUILayout.LabelField("Settings", EditorStyles.boldLabel);
						var param = action.ParamCanvasGroupAlphaFadeTo;

						DrawParam("Duration", param.duration);
						DrawParam("Alpha", param.alpha);
					}
					break;
				case GActionFsm.ActionType.Sequence:
					{
						var param = action.ParamSequence;
						param.foldout = EditorGUILayout.Foldout(param.foldout, "Settings", true, EditorStyles.foldoutHeader);

						if (param.foldout)
						{
							EditorGUI.indentLevel++;

							// 显示数组大小控制
							int newSize = EditorGUILayout.IntField("Count", param.actions.Length);
							if (newSize != param.actions.Length)
							{
								System.Array.Resize(ref param.actions, newSize);
								for (int i = 0; i < param.actions.Length; i++)
								{
									if (param.actions[i] == null)
									{
										param.actions[i] = new PMGAction();
									}
								}
							}

							EditorGUILayout.Space();

							// 递归显示每个子动作的配置
							for (int i = 0; i < param.actions.Length; i++)
							{
								// 为嵌套的子动作创建更深的背景色，并显示深度信息
								var bgColor = depth % 2 == 0 ? "box" : "helpbox";
								EditorGUILayout.BeginVertical(bgColor);

								if (param.actions[i] == null)
								{
									param.actions[i] = new PMGAction();
								}

								var nestedSubAction = param.actions[i];

								// 添加嵌套子动作的折叠功能，显示深度信息
								EditorGUILayout.BeginHorizontal();
								string foldoutLabel = $"Action {i}";
								nestedSubAction.foldout = EditorGUILayout.Foldout(nestedSubAction.foldout, foldoutLabel, true, EditorStyles.foldout);
								EditorGUILayout.EndHorizontal();

								if (nestedSubAction.foldout)
								{
									EditorGUILayout.Space();

									// 递归显示属性
									DrawSubActionProperties(nestedSubAction, depth + 1);
								}

								EditorGUILayout.EndVertical();
								EditorGUILayout.Space();
							}
						}
					}
					break;
				case GActionFsm.ActionType.Spawn:
					{
						var param = action.ParamSpawn;
						param.foldout = EditorGUILayout.Foldout(param.foldout, "Settings", true, EditorStyles.foldoutHeader);

						if (param.foldout)
						{
							EditorGUI.indentLevel++;

							// 显示数组大小控制
							int newSize = EditorGUILayout.IntField("Count", param.actions.Length);
							if (newSize != param.actions.Length)
							{
								System.Array.Resize(ref param.actions, newSize);
								for (int i = 0; i < param.actions.Length; i++)
								{
									if (param.actions[i] == null)
									{
										param.actions[i] = new PMGAction();
									}
								}
							}

							EditorGUILayout.Space();

							// 递归显示每个子动作的配置
							for (int i = 0; i < param.actions.Length; i++)
							{
								// 为嵌套的子动作创建更深的背景色，并显示深度信息
								var bgColor = depth % 2 == 0 ? "box" : "helpbox";
								EditorGUILayout.BeginVertical(bgColor);

								if (param.actions[i] == null)
								{
									param.actions[i] = new PMGAction();
								}

								var nestedSubAction = param.actions[i];

								// 添加嵌套子动作的折叠功能，显示深度信息
								EditorGUILayout.BeginHorizontal();
								string foldoutLabel = $"Action {i}";
								nestedSubAction.foldout = EditorGUILayout.Foldout(nestedSubAction.foldout, foldoutLabel, true, EditorStyles.foldout);
								EditorGUILayout.EndHorizontal();

								if (nestedSubAction.foldout)
								{
									EditorGUILayout.Space();

									// 递归显示属性
									DrawSubActionProperties(nestedSubAction, depth + 1);
								}

								EditorGUILayout.EndVertical();
								EditorGUILayout.Space();
							}
						}
					}
					break;
				case GActionFsm.ActionType.Repeat:
					{
						var param = action.ParamRepeate;

						DrawParam("Times", param.times);

						EditorGUILayout.LabelField("SubAction", EditorStyles.boldLabel);

						EditorGUI.indentLevel++;

						var subAction = param.action[0];
						DrawSubActionProperties(subAction, depth + 1);

						EditorGUI.indentLevel--;

						EditorGUILayout.Space();
					}
					break;
				case GActionFsm.ActionType.RepeatForever:
					{
						var param = action.ParamRepeatForever;

						EditorGUILayout.LabelField("SubAction", EditorStyles.boldLabel);

						EditorGUI.indentLevel++;

						var subAction = param.action[0];
						DrawSubActionProperties(subAction, depth + 1, GetIntervalActions());

						EditorGUI.indentLevel--;

						EditorGUILayout.Space();
					}
					break;
			}
		}

		private List<GActionFsm.ActionType> GetIntervalActions()
		{
			var list = new List<GActionFsm.ActionType>()
			{
				GActionFsm.ActionType.DelayTime,
				GActionFsm.ActionType.MoveBy,
				GActionFsm.ActionType.MoveTo,
				GActionFsm.ActionType.RotateBy3D,
				GActionFsm.ActionType.RotateBy,
				GActionFsm.ActionType.RotateTo,
				GActionFsm.ActionType.ScaleTo,
				GActionFsm.ActionType.ScaleBy,
				GActionFsm.ActionType.FadeTo,
				GActionFsm.ActionType.TintTo,
				GActionFsm.ActionType.BlendTo,
				GActionFsm.ActionType.Flash,
				GActionFsm.ActionType.FlashWithCurve,
				GActionFsm.ActionType.Sequence,
				GActionFsm.ActionType.Spawn,
				GActionFsm.ActionType.Repeat,
				GActionFsm.ActionType.RepeatForever,
				GActionFsm.ActionType.BezierTo,
				GActionFsm.ActionType.CanvasGroupAlphaFadeTo,
			};

			return list;
		}

		private void DrawParam<T>(string fieldName, PMParam<T> param)
		{
			EditorGUILayout.LabelField(fieldName, EditorStyles.boldLabel);

			param.UseStoreData = EditorGUILayout.Toggle("UseStoreData", param.UseStoreData);

			if (param.UseStoreData)
			{
				param.StoreKey = EditorGUILayout.TextField("StoreKey", param.StoreKey);
			}
			else
			{
				if (typeof(T) == typeof(string))
				{
					param.RawValue = (T)(object)EditorGUILayout.TextField("Value", (string)(object)param.RawValue);
				}
				else if (typeof(T) == typeof(int))
				{
					param.RawValue = (T)(object)EditorGUILayout.IntField("Value", (int)(object)param.RawValue);
				}
				else if (typeof(T) == typeof(long))
				{
					param.RawValue = (T)(object)EditorGUILayout.LongField("Value", (long)(object)param.RawValue);
				}
				else if (typeof(T) == typeof(float))
				{
					param.RawValue = (T)(object)EditorGUILayout.FloatField("Value", (float)(object)param.RawValue);
				}
				else if (typeof(T) == typeof(double))
				{
					param.RawValue = (T)(object)EditorGUILayout.DoubleField("Value", (double)(object)param.RawValue);
				}
				else if (typeof(T) == typeof(bool))
				{
					param.RawValue = (T)(object)EditorGUILayout.Toggle("Value", (bool)(object)param.RawValue);
				}
				else if (typeof(T) == typeof(Vector2))
				{
					param.RawValue = (T)(object)EditorGUILayout.Vector2Field("Value", (Vector2)(object)param.RawValue);
				}
				else if (typeof(T) == typeof(Vector3))
				{
					param.RawValue = (T)(object)EditorGUILayout.Vector3Field("Value", (Vector3)(object)param.RawValue);
				}
				else if (typeof(T) == typeof(Vector4))
				{
					param.RawValue = (T)(object)EditorGUILayout.Vector4Field("Value", (Vector4)(object)param.RawValue);
				}
				else if (typeof(T) == typeof(Color))
				{
					param.RawValue = (T)(object)EditorGUILayout.ColorField("Value", (Color)(object)param.RawValue);
				}
				else if(typeof(T) == typeof(AnimationCurve))
				{
					param.RawValue = (T)(object)EditorGUILayout.CurveField("Value", (AnimationCurve)(object)param.RawValue);
				}
				else
				{
					EditorGUILayout.LabelField("不支持的类型");
				}
			}

			EditorGUILayout.Space();
		}
	}
}